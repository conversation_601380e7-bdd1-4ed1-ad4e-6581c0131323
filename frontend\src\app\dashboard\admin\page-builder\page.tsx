'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { UserRole } from '@/types/auth'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { AdminDashboardHeader } from '@/components/admin/AdminDashboardHeader'
import { PageBuilder } from '@/components/admin/PageBuilder'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { toast } from 'sonner'
import {
  Wand2,
  Layout,
  Plus,
  Eye,
  Edit,
  Trash2,
  Upload,
  Download,
  BarChart3,
  RefreshCw,
  Sparkles,
  Globe,
  FileText,
  Search,
  Filter,
  SortAsc,
  SortDesc,
  MoreHorizontal,
  Copy,
  ExternalLink,
  Archive,
  Trash,
  AlertTriangle,
  CheckCircle2,
  Clock,
  Users,
  TrendingUp
} from 'lucide-react'
import { PageProject } from '@/types/page-builder'

export default function PageBuilderPage() {
  const { user, profile } = useAuth()
  const [projects, setProjects] = useState<PageProject[]>([])
  const [filteredProjects, setFilteredProjects] = useState<PageProject[]>([])
  const [loading, setLoading] = useState(true)
  const [showBuilder, setShowBuilder] = useState(false)
  const [currentProject, setCurrentProject] = useState<PageProject | undefined>()
  const [previewProject, setPreviewProject] = useState<PageProject | undefined>()
  const [showPreview, setShowPreview] = useState(false)
  const [stats, setStats] = useState<any>({})

  // فلاتر البحث والترتيب
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'published' | 'draft'>('all')
  const [generationModeFilter, setGenerationModeFilter] = useState<'all' | 'ai' | 'template' | 'manual'>('all')
  const [sortBy, setSortBy] = useState<'name' | 'createdAt' | 'updatedAt'>('updatedAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  // حالات الحذف المتعدد
  const [selectedProjects, setSelectedProjects] = useState<string[]>([])
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [projectToDelete, setProjectToDelete] = useState<PageProject | null>(null)
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false)

  // جلب المشاريع
  const fetchProjects = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/page-builder?include_unpublished=true')
      const data = await response.json()
      
      if (response.ok) {
        setProjects(data.projects)
        setStats(data.stats)
      } else {
        toast.error(data.error || 'خطأ في جلب المشاريع')
      }
    } catch (error) {
      console.error('Error fetching projects:', error)
      toast.error('خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProjects()
  }, [])

  // تطبيق الفلاتر والبحث
  useEffect(() => {
    let filtered = [...projects]

    // البحث
    if (searchTerm) {
      filtered = filtered.filter(project =>
        project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // فلتر الحالة
    if (statusFilter !== 'all') {
      filtered = filtered.filter(project =>
        statusFilter === 'published' ? project.isPublished : !project.isPublished
      )
    }

    // فلتر طريقة التوليد
    if (generationModeFilter !== 'all') {
      filtered = filtered.filter(project => project.generationMode === generationModeFilter)
    }

    // الترتيب
    filtered.sort((a, b) => {
      const aValue = a[sortBy]
      const bValue = b[sortBy]

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    setFilteredProjects(filtered)
  }, [projects, searchTerm, statusFilter, generationModeFilter, sortBy, sortOrder])

  // إنشاء مشروع جديد
  const createNewProject = () => {
    setCurrentProject(undefined)
    setShowBuilder(true)
  }

  // تحرير مشروع
  const editProject = (project: PageProject) => {
    setCurrentProject(project)
    setShowBuilder(true)
  }

  // حفظ مشروع
  const saveProject = async (project: PageProject) => {
    try {
      const url = project.id && projects.find(p => p.id === project.id) 
        ? `/api/page-builder/${project.id}` 
        : '/api/page-builder'
      const method = project.id && projects.find(p => p.id === project.id) ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(project)
      })
      
      const result = await response.json()
      
      if (response.ok) {
        toast.success(result.message)
        fetchProjects()
        setShowBuilder(false)
      } else {
        toast.error(result.error)
      }
    } catch (error) {
      console.error('Error saving project:', error)
      toast.error('خطأ في حفظ المشروع')
    }
  }

  // معاينة مشروع
  const handlePreview = (project: PageProject) => {
    setPreviewProject(project)
    setShowPreview(true)
  }

  // نشر مشروع
  const publishProject = async (project: PageProject) => {
    try {
      const response = await fetch('/api/page-builder', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'publish',
          projectIds: [project.id]
        })
      })
      
      const result = await response.json()
      
      if (response.ok) {
        toast.success('تم نشر المشروع بنجاح')
        fetchProjects()
      } else {
        toast.error(result.error)
      }
    } catch (error) {
      console.error('Error publishing project:', error)
      toast.error('خطأ في نشر المشروع')
    }
  }

  // حذف مشروع واحد
  const deleteProject = async (project: PageProject) => {
    try {
      const response = await fetch(`/api/page-builder?ids=${project.id}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (response.ok) {
        toast.success(`تم حذف المشروع "${project.name}" بنجاح`)
        fetchProjects()
        setShowDeleteDialog(false)
        setProjectToDelete(null)
      } else {
        toast.error(result.error)
      }
    } catch (error) {
      console.error('Error deleting project:', error)
      toast.error('خطأ في حذف المشروع')
    }
  }

  // حذف مشاريع متعددة
  const deleteBulkProjects = async () => {
    try {
      const response = await fetch(`/api/page-builder?ids=${selectedProjects.join(',')}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (response.ok) {
        toast.success(`تم حذف ${selectedProjects.length} مشروع بنجاح`)
        fetchProjects()
        setSelectedProjects([])
        setShowBulkDeleteDialog(false)
      } else {
        toast.error(result.error)
      }
    } catch (error) {
      console.error('Error deleting projects:', error)
      toast.error('خطأ في حذف المشاريع')
    }
  }

  // نسخ مشروع
  const duplicateProject = async (project: PageProject) => {
    try {
      const duplicatedProject = {
        ...project,
        id: undefined,
        name: `نسخة من ${project.name}`,
        isPublished: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      const response = await fetch('/api/page-builder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(duplicatedProject)
      })

      const result = await response.json()

      if (response.ok) {
        toast.success('تم نسخ المشروع بنجاح')
        fetchProjects()
      } else {
        toast.error(result.error)
      }
    } catch (error) {
      console.error('Error duplicating project:', error)
      toast.error('خطأ في نسخ المشروع')
    }
  }

  // تبديل تحديد المشروع
  const toggleProjectSelection = (projectId: string) => {
    setSelectedProjects(prev =>
      prev.includes(projectId)
        ? prev.filter(id => id !== projectId)
        : [...prev, projectId]
    )
  }

  // تحديد/إلغاء تحديد جميع المشاريع
  const toggleSelectAll = () => {
    if (selectedProjects.length === filteredProjects.length) {
      setSelectedProjects([])
    } else {
      setSelectedProjects(filteredProjects.map(p => p.id))
    }
  }

  // تصدير مشروع كـ HTML
  const exportProjectAsHTML = async (project: PageProject) => {
    try {
      const response = await fetch('/api/page-builder/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId: project.id, format: 'html' })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${project.name}.html`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        toast.success('تم تصدير المشروع بنجاح')
      } else {
        toast.error('خطأ في تصدير المشروع')
      }
    } catch (error) {
      console.error('Error exporting project:', error)
      toast.error('خطأ في تصدير المشروع')
    }
  }

  // نشر مشاريع متعددة
  const publishBulkProjects = async () => {
    try {
      const response = await fetch('/api/page-builder', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'publish',
          projectIds: selectedProjects
        })
      })

      const result = await response.json()

      if (response.ok) {
        toast.success(`تم نشر ${selectedProjects.length} مشروع بنجاح`)
        fetchProjects()
        setSelectedProjects([])
      } else {
        toast.error(result.error)
      }
    } catch (error) {
      console.error('Error publishing projects:', error)
      toast.error('خطأ في نشر المشاريع')
    }
  }

  const getGenerationModeIcon = (mode: string) => {
    switch (mode) {
      case 'ai': return <Sparkles className="h-4 w-4" />
      case 'template': return <Layout className="h-4 w-4" />
      case 'manual': return <Edit className="h-4 w-4" />
      default: return <FileText className="h-4 w-4" />
    }
  }

  const getGenerationModeLabel = (mode: string) => {
    switch (mode) {
      case 'ai': return 'ذكاء اصطناعي'
      case 'template': return 'من قالب'
      case 'manual': return 'يدوي'
      default: return 'غير محدد'
    }
  }

  if (showBuilder) {
    return (
      <ProtectedRoute requiredRole={UserRole.ADMIN}>
        <div className="h-screen flex flex-col">
          <AdminDashboardHeader />
          <div className="flex-1">
            <PageBuilder
              project={currentProject}
              onSave={saveProject}
              onPreview={handlePreview}
              onPublish={publishProject}
            />
          </div>
          <div className="border-t p-4 bg-background">
            <Button 
              variant="outline" 
              onClick={() => setShowBuilder(false)}
            >
              العودة للمشاريع
            </Button>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN}>
      <div className="min-h-screen bg-background">
        <AdminDashboardHeader />
        
        <div className="container mx-auto px-4 py-8">
          {/* العنوان والإحصائيات */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold arabic-text flex items-center gap-3">
                <Wand2 className="h-8 w-8 text-primary" />
                بناء الصفحات الذكية
              </h1>
              <p className="text-muted-foreground mt-2">
                إنشاء وإدارة صفحات الويب باستخدام الذكاء الاصطناعي
              </p>
            </div>

            <div className="flex gap-2">
              {selectedProjects.length > 0 && (
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={publishBulkProjects}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    نشر المحدد ({selectedProjects.length})
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => setShowBulkDeleteDialog(true)}
                  >
                    <Trash className="h-4 w-4 mr-2" />
                    حذف المحدد ({selectedProjects.length})
                  </Button>
                </div>
              )}
              <Button onClick={() => fetchProjects()} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                تحديث
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    إنشاء جديد
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>إنشاء مشروع جديد</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={createNewProject}>
                    <Wand2 className="h-4 w-4 mr-2" />
                    بالذكاء الاصطناعي
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={createNewProject}>
                    <Layout className="h-4 w-4 mr-2" />
                    من قالب
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={createNewProject}>
                    <Edit className="h-4 w-4 mr-2" />
                    مشروع فارغ
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* شريط البحث والفلاتر */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex flex-col lg:flex-row gap-4">
                {/* البحث */}
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="البحث في المشاريع..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pr-10"
                    />
                  </div>
                </div>

                {/* فلاتر */}
                <div className="flex gap-2">
                  <Select value={statusFilter} onValueChange={(value: any) => setStatusFilter(value)}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="الحالة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الحالات</SelectItem>
                      <SelectItem value="published">منشور</SelectItem>
                      <SelectItem value="draft">مسودة</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={generationModeFilter} onValueChange={(value: any) => setGenerationModeFilter(value)}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="طريقة التوليد" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الطرق</SelectItem>
                      <SelectItem value="ai">ذكاء اصطناعي</SelectItem>
                      <SelectItem value="template">من قالب</SelectItem>
                      <SelectItem value="manual">يدوي</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
                    const [field, order] = value.split('-')
                    setSortBy(field as any)
                    setSortOrder(order as any)
                  }}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="الترتيب" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="updatedAt-desc">الأحدث</SelectItem>
                      <SelectItem value="updatedAt-asc">الأقدم</SelectItem>
                      <SelectItem value="name-asc">الاسم (أ-ي)</SelectItem>
                      <SelectItem value="name-desc">الاسم (ي-أ)</SelectItem>
                      <SelectItem value="createdAt-desc">تاريخ الإنشاء</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* نتائج البحث */}
              <div className="flex items-center justify-between mt-4 pt-4 border-t">
                <div className="flex items-center gap-4">
                  <span className="text-sm text-muted-foreground">
                    عرض {filteredProjects.length} من {projects.length} مشروع
                  </span>
                  {filteredProjects.length > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={toggleSelectAll}
                    >
                      {selectedProjects.length === filteredProjects.length ? 'إلغاء تحديد الكل' : 'تحديد الكل'}
                    </Button>
                  )}
                </div>

                {(searchTerm || statusFilter !== 'all' || generationModeFilter !== 'all') && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSearchTerm('')
                      setStatusFilter('all')
                      setGenerationModeFilter('all')
                    }}
                  >
                    مسح الفلاتر
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* بطاقات الإحصائيات */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي المشاريع</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.published || 0} منشور، {stats.unpublished || 0} مسودة
                </p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-500">+{stats.thisMonth || 0} هذا الشهر</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">مولد بالذكاء الاصطناعي</CardTitle>
                <Sparkles className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.byGenerationMode?.ai || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {Math.round(((stats.byGenerationMode?.ai || 0) / (stats.total || 1)) * 100)}% من المشاريع
                </p>
                <div className="w-full bg-gray-200 rounded-full h-1.5 mt-2">
                  <div
                    className="bg-blue-600 h-1.5 rounded-full"
                    style={{ width: `${Math.round(((stats.byGenerationMode?.ai || 0) / (stats.total || 1)) * 100)}%` }}
                  ></div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">من القوالب</CardTitle>
                <Layout className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.byGenerationMode?.template || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {Math.round(((stats.byGenerationMode?.template || 0) / (stats.total || 1)) * 100)}% من المشاريع
                </p>
                <div className="w-full bg-gray-200 rounded-full h-1.5 mt-2">
                  <div
                    className="bg-green-600 h-1.5 rounded-full"
                    style={{ width: `${Math.round(((stats.byGenerationMode?.template || 0) / (stats.total || 1)) * 100)}%` }}
                  ></div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">المنشورة</CardTitle>
                <Globe className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.published || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {Math.round(((stats.published || 0) / (stats.total || 1)) * 100)}% من المشاريع
                </p>
                <div className="flex items-center mt-2">
                  <Users className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-500">{stats.totalViews || 0} مشاهدة</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* قائمة المشاريع */}
          {loading ? (
            <div className="text-center py-12">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>جاري تحميل المشاريع...</p>
            </div>
          ) : filteredProjects.length === 0 ? (
            projects.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <Wand2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">لا توجد مشاريع</h3>
                  <p className="text-muted-foreground mb-4">
                    ابدأ بإنشاء مشروعك الأول باستخدام الذكاء الاصطناعي
                  </p>
                  <Button onClick={createNewProject}>
                    <Plus className="h-4 w-4 mr-2" />
                    إنشاء مشروع جديد
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <Search className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">لا توجد نتائج</h3>
                  <p className="text-muted-foreground mb-4">
                    لم يتم العثور على مشاريع تطابق معايير البحث
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchTerm('')
                      setStatusFilter('all')
                      setGenerationModeFilter('all')
                    }}
                  >
                    مسح الفلاتر
                  </Button>
                </CardContent>
              </Card>
            )
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredProjects.map((project) => (
                <Card key={project.id} className={`hover:shadow-lg transition-all duration-200 ${
                  selectedProjects.includes(project.id) ? 'ring-2 ring-primary' : ''
                }`}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        <input
                          type="checkbox"
                          checked={selectedProjects.includes(project.id)}
                          onChange={() => toggleProjectSelection(project.id)}
                          className="mt-1"
                        />
                        <div className="flex-1">
                          <CardTitle className="text-lg arabic-text line-clamp-1">
                            {project.name}
                          </CardTitle>
                          <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                            {project.description}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {project.isPublished ? (
                          <Badge variant="default">
                            <Globe className="h-3 w-3 mr-1" />
                            منشور
                          </Badge>
                        ) : (
                          <Badge variant="secondary">
                            <Clock className="h-3 w-3 mr-1" />
                            مسودة
                          </Badge>
                        )}

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handlePreview(project)}>
                              <Eye className="h-4 w-4 mr-2" />
                              معاينة
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => editProject(project)}>
                              <Edit className="h-4 w-4 mr-2" />
                              تحرير
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => duplicateProject(project)}>
                              <Copy className="h-4 w-4 mr-2" />
                              نسخ
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => exportProjectAsHTML(project)}>
                              <Download className="h-4 w-4 mr-2" />
                              تصدير HTML
                            </DropdownMenuItem>
                            {project.isPublished ? (
                              <DropdownMenuItem>
                                <ExternalLink className="h-4 w-4 mr-2" />
                                فتح الصفحة
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem onClick={() => publishProject(project)}>
                                <Upload className="h-4 w-4 mr-2" />
                                نشر المشروع
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => {
                                setProjectToDelete(project)
                                setShowDeleteDialog(true)
                              }}
                              className="text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              حذف
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        {getGenerationModeIcon(project.generationMode)}
                        <span>{getGenerationModeLabel(project.generationMode)}</span>
                      </div>
                      <span className="text-muted-foreground">
                        {project.components.length} مكون
                      </span>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
                      <div>
                        <span className="font-medium">تاريخ الإنشاء:</span>
                        <br />
                        {new Date(project.createdAt).toLocaleDateString('ar-MA')}
                      </div>
                      <div>
                        <span className="font-medium">آخر تحديث:</span>
                        <br />
                        {new Date(project.updatedAt).toLocaleDateString('ar-MA')}
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handlePreview(project)}
                        className="flex-1"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        معاينة
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => editProject(project)}
                        className="flex-1"
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        تحرير
                      </Button>
                    </div>

                    {!project.isPublished ? (
                      <Button
                        size="sm"
                        onClick={() => publishProject(project)}
                        className="w-full"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        نشر المشروع
                      </Button>
                    ) : (
                      <div className="flex items-center justify-center gap-2 p-2 bg-green-50 rounded-lg">
                        <CheckCircle2 className="h-4 w-4 text-green-600" />
                        <span className="text-sm text-green-600 font-medium">منشور ومتاح</span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* حوار المعاينة */}
        <Dialog open={showPreview} onOpenChange={setShowPreview}>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="arabic-text">
                معاينة: {previewProject?.name}
              </DialogTitle>
              <DialogDescription>
                معاينة الصفحة كما ستظهر للزوار
              </DialogDescription>
            </DialogHeader>

            {previewProject && (
              <div className="bg-white rounded-lg overflow-hidden border">
                <div className="space-y-0">
                  {previewProject.components.map((component) => (
                    <div
                      key={component.id}
                      style={{
                        height: component.size.height,
                        backgroundColor: component.props.style?.backgroundColor || '#f9fafb',
                        color: component.props.style?.color || '#111827',
                        padding: component.props.style?.padding || '1rem',
                        textAlign: component.props.style?.textAlign || 'right'
                      }}
                      className="arabic-text"
                    >
                      {component.props.content || `مكون ${component.type}`}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* حوار تأكيد حذف مشروع واحد */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="arabic-text flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-destructive" />
                تأكيد الحذف
              </AlertDialogTitle>
              <AlertDialogDescription>
                هل أنت متأكد من حذف المشروع "{projectToDelete?.name}"؟
                <br />
                <span className="text-destructive font-medium">
                  هذا الإجراء لا يمكن التراجع عنه.
                </span>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>إلغاء</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => projectToDelete && deleteProject(projectToDelete)}
                className="bg-destructive hover:bg-destructive/90"
              >
                حذف المشروع
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* حوار تأكيد الحذف المتعدد */}
        <AlertDialog open={showBulkDeleteDialog} onOpenChange={setShowBulkDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="arabic-text flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-destructive" />
                تأكيد الحذف المتعدد
              </AlertDialogTitle>
              <AlertDialogDescription>
                هل أنت متأكد من حذف {selectedProjects.length} مشروع؟
                <br />
                <span className="text-destructive font-medium">
                  هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع المشاريع المحددة.
                </span>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>إلغاء</AlertDialogCancel>
              <AlertDialogAction
                onClick={deleteBulkProjects}
                className="bg-destructive hover:bg-destructive/90"
              >
                حذف {selectedProjects.length} مشروع
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </ProtectedRoute>
  )
}
